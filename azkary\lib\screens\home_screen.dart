import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';

import '../services/azkar_provider.dart';
import '../services/feedback_provider.dart';
import '../services/daily_ayah_provider.dart';
import '../models/azkar_model.dart';
import '../utils/logger.dart';
import 'azkar_list_screen.dart';
import 'custom_azkar_screen.dart';
import 'settings_screen.dart';
import '../widgets/page_transitions.dart';
import '../widgets/daily_allah_name.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/daily_ayah_dialog.dart';
import '../widgets/islamic_pattern.dart';
import '../widgets/enhanced_card.dart';
import '../widgets/enhanced_animations.dart';
import '../widgets/prayer_times_bar.dart';
import '../services/prayer_provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // التأكد من تحميل البيانات
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final provider = Provider.of<AzkarProvider>(context, listen: false);
      AppLogger.info(
        'Home screen initialized, categories count: ${provider.categories.length}',
      );

      // إذا كانت التصنيفات فارغة، قم بإعادة تحميل البيانات
      if (provider.categories.isEmpty) {
        AppLogger.info('Categories are empty, reloading data...');
        await _reloadData();
      } else {
        AppLogger.info('Categories already loaded:');
        for (var category in provider.categories) {
          AppLogger.info('- ${category.name} (${category.icon})');
        }
      }

      // تهيئة مزود أوقات الصلاة
      if (mounted) {
        final prayerProvider = Provider.of<PrayerProvider>(
          context,
          listen: false,
        );
        prayerProvider.initialize();
      }
    });
  }

  // دالة لإعادة تحميل البيانات
  Future<void> _reloadData() async {
    try {
      AppLogger.info('Reloading data from home screen');
      final provider = Provider.of<AzkarProvider>(context, listen: false);

      // إعادة تهيئة قاعدة البيانات وتحميل البيانات
      await provider.reloadData();

      AppLogger.info(
        'Data reloaded, categories count: ${provider.categories.length}',
      );
      if (provider.categories.isNotEmpty) {
        AppLogger.info('Categories loaded successfully:');
        for (var category in provider.categories) {
          AppLogger.info('- ${category.name} (${category.icon})');
        }
      } else {
        AppLogger.warning(
          'No categories loaded after reload, forcing another reload',
        );
        // محاولة إعادة التحميل مرة أخرى
        await provider.reloadData();
      }

      // تحديث واجهة المستخدم
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      AppLogger.error('Error reloading data from home screen: $e');
      // محاولة إعادة التحميل في حالة الخطأ
      if (mounted) {
        final provider = Provider.of<AzkarProvider>(context, listen: false);
        // محاولة إعادة التحميل مرة أخرى
        await provider.reloadData();
      }

      if (mounted) {
        setState(() {});
      }
    }
  }

  // دالة لتنسيق التاريخ بالعربية
  String _getFormattedDate() {
    final now = DateTime.now();
    final formatter = DateFormat('EEEE، d MMMM yyyy', 'ar');
    return formatter.format(now);
  }

  @override
  Widget build(BuildContext context) {
    // استخدام ألوان السمة بدلاً من الألوان الثابتة
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'أذكاري',
        actions: [
          // زر الإعدادات
          IconButton(
            icon: Icon(
              Icons.settings,
              color: theme.colorScheme.onSurface,
              size: 24,
            ),
            tooltip: 'الإعدادات',
            onPressed: () {
              HapticFeedback.lightImpact(); // تأثير اهتزاز خفيف
              Navigator.push(
                context,
                PageTransition(
                  type: PageTransitionType.rightToLeftWithFade,
                  child: const SettingsScreen(),
                  duration: const Duration(milliseconds: 400),
                  curve: Curves.easeInOut,
                ),
              );
            },
          ),
        ],
        leading: IconButton(
          icon: Icon(Icons.menu, color: theme.colorScheme.onSurface, size: 24),
          onPressed: () {
            HapticFeedback.lightImpact(); // تأثير اهتزاز خفيف
            Scaffold.of(context).openDrawer();
          },
        ),
      ),
      // تم إزالة زر الإضافة العائم واستبداله بزر في شبكة التصنيفات
      body: Stack(
        children: [
          // خلفية إسلامية
          Positioned.fill(
            child: Opacity(
              opacity: 0.05,
              child: IslamicPattern(color: theme.colorScheme.primary),
            ),
          ),

          // محتوى الصفحة
          Consumer<AzkarProvider>(
            builder: (context, provider, child) {
              if (provider.isLoading) {
                return const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                  ),
                );
              }

              // التحقق من وجود البيانات
              if (provider.categories.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'لا توجد بيانات متاحة',
                        style: TextStyle(fontSize: 18),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _reloadData,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text('إعادة تحميل البيانات'),
                      ),
                    ],
                  ),
                );
              }

              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // شريط أوقات الصلاة
                    const PrayerTimesBar(),

                    // قسم ذكر اليوم
                    if (provider.dailyZikr != null)
                      _buildDailyZikrSection(provider.dailyZikr!),

                    // قسم تصنيفات الأذكار
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'تصنيفات الأذكار',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color:
                              theme
                                  .colorScheme
                                  .onSurface, // استخدام لون النص على السطح من السمة
                        ),
                      ),
                    ),

                    // شبكة التصنيفات
                    _buildCategoriesGrid(provider.categories),

                    // قسم الأذكار الخاصة
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'أذكاري الخاصة',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          Text(
                            '${provider.customAzkar.length} ذكر',
                            style: TextStyle(
                              fontSize: 14,
                              color: theme.colorScheme.onSurface.withAlpha(153),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // زر الأذكار الخاصة
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const CustomAzkarScreen(),
                            ),
                          );
                        },
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          // الحصول على حجم الشاشة
                          padding: EdgeInsets.all(
                            MediaQuery.of(context).size.width < 360 ? 12 : 16,
                          ),
                          decoration: BoxDecoration(
                            color:
                                theme.cardColor, // استخدام لون البطاقة من الثيم
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color:
                                  theme.brightness == Brightness.dark
                                      ? const Color(
                                        0x4D9E9E9E,
                                      ) // حدود مثل تويتر
                                      : Colors.grey.withAlpha(30),
                              width: 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color:
                                    theme.brightness == Brightness.dark
                                        ? Colors.black.withAlpha(
                                          40,
                                        ) // ظل مثل تويتر
                                        : Colors.black.withAlpha(25),
                                spreadRadius: 1,
                                blurRadius: 5,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 50,
                                height: 50,
                                decoration: BoxDecoration(
                                  color: Colors.purple.withAlpha(51),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.bookmark,
                                  color: Colors.purple,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'أذكاري الخاصة',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.onSurface,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'أضف وعدل أذكارك الخاصة',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: theme.colorScheme.onSurface
                                            .withAlpha(153),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.arrow_forward_ios,
                                size: 16,
                                color: theme.colorScheme.onSurface.withAlpha(
                                  153,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // قسم اسم الله اليومي
                    const Padding(
                      padding: EdgeInsets.fromLTRB(16, 24, 16, 8),
                      child: Text(
                        'اسم الله اليومي',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    // مكون اسم الله اليومي
                    const DailyAllahName(),

                    // زر عرض الآية اليومية
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
                      child: SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () {
                            // تهيئة مزود الآية اليومية
                            final provider = Provider.of<DailyAyahProvider>(
                              context,
                              listen: false,
                            );
                            provider.initialize();

                            // عرض نافذة الآية اليومية
                            showDialog(
                              context: context,
                              builder: (context) => const DailyAyahDialog(),
                            );
                          },
                          icon: const Icon(Icons.menu_book),
                          label: const Text(
                            'عرض آية اليوم',
                            style: TextStyle(fontSize: 16),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              vertical: 16,
                              horizontal: 16,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // بناء قسم ذكر اليوم
  Widget _buildDailyZikrSection(Zikr dailyZikr) {
    final theme = Theme.of(context); // استخدام السمة

    return Hero(
      tag: 'daily-zikr',
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        width: double.infinity, // تأكد من أن العرض لا يتجاوز عرض الشاشة
        decoration: BoxDecoration(
          color: theme.cardColor, // استخدام لون البطاقة من الثيم
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color:
                theme.brightness == Brightness.dark
                    ? const Color(0x4D9E9E9E) // حدود مثل تويتر
                    : Colors.grey.withAlpha(30),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color:
                  theme.brightness == Brightness.dark
                      ? Colors.black.withAlpha(40) // ظل مثل تويتر
                      : Colors.black.withAlpha(25),
              spreadRadius: 2,
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize:
              MainAxisSize
                  .min, // تأكد من أن العمود لا يأخذ مساحة أكثر من اللازم
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  // استخدام Flexible لضمان التفاف النص إذا كان طويلاً
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withAlpha(25),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.auto_awesome,
                          color: theme.colorScheme.primary,
                          size: 18,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        // استخدام Flexible لضمان التفاف النص إذا كان طويلاً
                        child: Text(
                          'ذكري اليومي',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withAlpha(25),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getFormattedDate(),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              dailyZikr.text,
              style: TextStyle(
                fontSize: 16,
                height: 1.5,
                color: theme.colorScheme.onSurface,
              ),
              // تأكد من التفاف النص داخل حدود الشاشة
              softWrap: true,
              overflow: TextOverflow.visible,
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  // استخدام Flexible لضمان التفاف النص إذا كان طويلاً
                  child: Text(
                    'رواه ${dailyZikr.source}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.secondary,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Row(
                  mainAxisSize:
                      MainAxisSize
                          .min, // تأكد من أن الصف لا يأخذ مساحة أكثر من اللازم
                  children: [
                    _buildAnimatedIconButton(
                      Icons.copy_outlined,
                      'نسخ',
                      onPressed: () {
                        // نسخ النص إلى الحافظة
                        _copyToClipboard(dailyZikr.text);
                      },
                    ),
                    const SizedBox(width: 16),
                    _buildAnimatedIconButton(
                      Icons.share_outlined,
                      'مشاركة',
                      onPressed: () {
                        // مشاركة النص
                        _shareZikr(dailyZikr);
                      },
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بناء زر أيقونة متحرك
  Widget _buildAnimatedIconButton(
    IconData icon,
    String tooltip, {
    VoidCallback? onPressed,
  }) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(25),
              blurRadius: 4,
              spreadRadius: 1,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Tooltip(
          message: tooltip,
          child: Icon(icon, size: 18, color: theme.colorScheme.primary),
        ),
      ),
    );
  }

  // نسخ النص إلى الحافظة
  void _copyToClipboard(String text) {
    // تنفيذ نسخ النص إلى الحافظة
    Clipboard.setData(ClipboardData(text: text));

    // تنفيذ اهتزاز خفيف
    final feedbackProvider = Provider.of<FeedbackProvider>(
      context,
      listen: false,
    );
    feedbackProvider.lightHapticFeedback();

    // عرض رسالة تأكيد
    feedbackProvider.showSuccessSnackBar(
      context,
      'تم نسخ النص',
      icon: Icons.check_circle,
    );
  }

  // مشاركة الذكر
  void _shareZikr(Zikr zikr) {
    // تنفيذ مشاركة الذكر
    final text = '${zikr.text}\n\nرواه ${zikr.source}\n\nمن تطبيق أذكاري';

    // استخدام SharePlus بالطريقة الصحيحة
    final box = context.findRenderObject() as RenderBox?;
    SharePlus.instance.share(
      ShareParams(
        text: text,
        sharePositionOrigin:
            box != null ? box.localToGlobal(Offset.zero) & box.size : null,
      ),
    );

    // تنفيذ اهتزاز خفيف
    final feedbackProvider = Provider.of<FeedbackProvider>(
      context,
      listen: false,
    );
    feedbackProvider.lightHapticFeedback();

    // عرض رسالة تأكيد
    feedbackProvider.showInfoSnackBar(context, 'تمت مشاركة الذكر');
  }

  // بناء شبكة التصنيفات
  Widget _buildCategoriesGrid(List<Category> categories) {
    // الحصول على حجم الشاشة
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isMediumScreen = screenSize.width >= 360 && screenSize.width < 600;

    // تحديد عدد الأعمدة بناءً على حجم الشاشة
    // تعديل: جعل عدد الأعمدة 2 حتى في الشاشات الصغيرة
    int crossAxisCount = isSmallScreen ? 2 : (isMediumScreen ? 2 : 3);

    // تحديد نسبة العرض إلى الارتفاع
    double childAspectRatio =
        isSmallScreen ? 1.0 : (isMediumScreen ? 1.0 : 1.1);

    // إنشاء قائمة جديدة تحتوي على التصنيفات الحالية
    final List<Widget> gridItems = [];

    // إضافة عناصر التصنيفات الحالية
    for (var category in categories) {
      gridItems.add(_buildCategoryItem(category));
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: isSmallScreen ? 8 : 16,
        mainAxisSpacing: isSmallScreen ? 8 : 16,
        childAspectRatio: childAspectRatio,
      ),
      itemCount: gridItems.length,
      itemBuilder: (context, index) {
        return gridItems[index];
      },
    );
  }

  // بناء عنصر التصنيف
  Widget _buildCategoryItem(Category category) {
    final theme = Theme.of(context); // استخدام السمة

    // الحصول على حجم الشاشة
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;

    // تحديد الأيقونة بناءً على اسم التصنيف
    IconData iconData;
    Color iconBgColor;
    Color iconColor;

    // تحديد الأيقونة بناءً على اسم التصنيف أو الأيقونة
    if (category.name.contains('الصباح')) {
      iconData = Icons.wb_sunny_outlined;
      iconBgColor = Colors.amber.withAlpha(51);
      iconColor = Colors.amber;
    } else if (category.name.contains('المساء')) {
      iconData = Icons.nightlight_round;
      iconBgColor = Colors.indigo.withAlpha(51);
      iconColor = Colors.indigo;
    } else if (category.name.contains('النوم')) {
      iconData = Icons.bed;
      iconBgColor = Colors.teal.withAlpha(51);
      iconColor = Colors.teal;
    } else if (category.name.contains('الاستيقاظ')) {
      iconData = Icons.alarm;
      iconBgColor = Colors.orange.withAlpha(51);
      iconColor = Colors.orange;
    } else if (category.name.contains('الصلاة')) {
      iconData = Icons.mosque;
      iconBgColor = Colors.green.withAlpha(51);
      iconColor = Colors.green;
    } else if (category.name.contains('الاستغفار')) {
      iconData = Icons.favorite;
      iconBgColor = const Color(0xFFFF0000).withAlpha(51); // أحمر نقي واضح
      iconColor = const Color(0xFFFF0000); // أحمر نقي واضح
    } else {
      // الحالة الافتراضية
      switch (category.icon) {
        case 'sun':
          iconData = Icons.wb_sunny_outlined;
          iconBgColor = Colors.amber.withAlpha(51);
          iconColor = Colors.amber;
          break;
        case 'moon':
          iconData = Icons.nightlight_round;
          iconBgColor = Colors.indigo.withAlpha(51);
          iconColor = Colors.indigo;
          break;
        case 'bed':
          iconData = Icons.bed;
          iconBgColor = Colors.teal.withAlpha(51);
          iconColor = Colors.teal;
          break;
        case 'alarm':
          iconData = Icons.alarm;
          iconBgColor = Colors.orange.withAlpha(51);
          iconColor = Colors.orange;
          break;
        case 'prayer':
          iconData = Icons.mosque;
          iconBgColor = Colors.green.withAlpha(51);
          iconColor = Colors.green;
          break;
        case 'heart':
          iconData = Icons.favorite;
          iconBgColor = const Color(0xFFFF0000).withAlpha(51); // أحمر نقي واضح
          iconColor = const Color(0xFFFF0000); // أحمر نقي واضح
          break;
        case 'travel':
          iconData = Icons.directions_car;
          iconBgColor = const Color(0xFF8B4513).withAlpha(51); // بني واضح
          iconColor = const Color(0xFF8B4513); // بني واضح
          break;
        case 'food':
          iconData = Icons.restaurant;
          iconBgColor = const Color(0xFFFFFF00).withAlpha(51); // أصفر نقي واضح
          iconColor = const Color(0xFFFFFF00); // أصفر نقي واضح
          break;
        default:
          iconData = Icons.auto_awesome;
          iconBgColor = Colors.blue.withAlpha(51);
          iconColor = Colors.blue;
      }
    }

    return FadeInAnimation(
      delay: Duration(milliseconds: category.id * 100),
      child: Hero(
        tag: 'category-${category.id}',
        child: EnhancedCard(
          onTap: () {
            Navigator.push(
              context,
              PageTransition(
                type: PageTransitionType.rightToLeftWithFade,
                child: AzkarListScreen(category: category.name),
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              ),
            );
          },
          padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
          margin: EdgeInsets.zero,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: isSmallScreen ? 48 : 56,
                height: isSmallScreen ? 48 : 56,
                decoration: BoxDecoration(
                  color: iconBgColor,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: iconColor.withAlpha(50),
                      blurRadius: 8,
                      spreadRadius: 1,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  iconData,
                  color: iconColor,
                  size: isSmallScreen ? 24 : 28,
                ),
              ),
              SizedBox(height: isSmallScreen ? 12 : 16),
              Text(
                category.name,
                style: TextStyle(
                  fontSize: isSmallScreen ? 14 : 16,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: isSmallScreen ? 4 : 6),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 6 : 8,
                  vertical: isSmallScreen ? 2 : 4,
                ),
                decoration: BoxDecoration(
                  color: iconColor.withAlpha(30),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${category.count} ذكر',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 10 : 12,
                    fontWeight: FontWeight.bold,
                    color: iconColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
